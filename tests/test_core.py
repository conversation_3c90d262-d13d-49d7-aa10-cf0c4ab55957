"""Tests for the core module."""

import pytest

from pystarter.core import add_numbers, hello_world


class TestHelloWorld:
    """Test cases for the hello_world function."""

    def test_hello_world_no_name(self) -> None:
        """Test hello_world with no name provided."""
        result = hello_world()
        assert result == "Hello, World!"

    def test_hello_world_with_name(self) -> None:
        """Test hello_world with a name provided."""
        result = hello_world("Alice")
        assert result == "Hello, Alice!"

    def test_hello_world_with_empty_string(self) -> None:
        """Test hello_world with an empty string."""
        result = hello_world("")
        assert result == "Hello, !"

    @pytest.mark.parametrize(
        "name,expected",
        [
            ("<PERSON>", "Hello, <PERSON>!"),
            ("<PERSON>", "Hello, <PERSON>!"),
            ("123", "Hello, 123!"),
            ("Test User", "Hello, Test User!"),
        ],
    )
    def test_hello_world_parametrized(self, name: str, expected: str) -> None:
        """Test hello_world with various names."""
        result = hello_world(name)
        assert result == expected


class TestAddNumbers:
    """Test cases for the add_numbers function."""

    def test_add_positive_numbers(self) -> None:
        """Test adding two positive numbers."""
        result = add_numbers(2, 3)
        assert result == 5

    def test_add_negative_numbers(self) -> None:
        """Test adding two negative numbers."""
        result = add_numbers(-2, -3)
        assert result == -5

    def test_add_mixed_numbers(self) -> None:
        """Test adding positive and negative numbers."""
        result = add_numbers(-1, 1)
        assert result == 0

    def test_add_zero(self) -> None:
        """Test adding zero to a number."""
        result = add_numbers(5, 0)
        assert result == 5

    @pytest.mark.parametrize(
        "a,b,expected",
        [
            (0, 0, 0),
            (1, 1, 2),
            (10, 20, 30),
            (-5, 5, 0),
            (100, -50, 50),
        ],
    )
    def test_add_numbers_parametrized(self, a: int, b: int, expected: int) -> None:
        """Test add_numbers with various inputs."""
        result = add_numbers(a, b)
        assert result == expected


@pytest.mark.integration
class TestIntegration:
    """Integration tests."""

    def test_module_imports(self) -> None:
        """Test that all expected functions can be imported."""
        from pystarter import hello_world
        
        # Test that the imported function works
        result = hello_world("Integration Test")
        assert result == "Hello, Integration Test!"

    def test_package_version(self) -> None:
        """Test that package version is accessible."""
        import pystarter
        
        assert hasattr(pystarter, "__version__")
        assert isinstance(pystarter.__version__, str)
        assert pystarter.__version__ == "0.1.0"
