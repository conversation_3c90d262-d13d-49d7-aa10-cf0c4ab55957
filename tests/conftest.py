"""Pytest configuration and fixtures."""

import pytest


@pytest.fixture
def sample_data() -> dict[str, str]:
    """Provide sample data for tests."""
    return {
        "name": "Test User",
        "email": "<EMAIL>",
        "role": "developer",
    }


@pytest.fixture(scope="session")
def test_config() -> dict[str, bool]:
    """Provide test configuration."""
    return {
        "debug": True,
        "strict_mode": True,
    }
