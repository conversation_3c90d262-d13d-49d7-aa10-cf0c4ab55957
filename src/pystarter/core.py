"""Core functionality for PyStarter."""

from typing import Optional


def hello_world(name: Optional[str] = None) -> str:
    """Return a greeting message.
    
    Args:
        name: Optional name to include in the greeting.
        
    Returns:
        A greeting message string.
        
    Examples:
        >>> hello_world()
        'Hello, World!'
        >>> hello_world("Alice")
        'Hello, <PERSON>!'
    """
    if name is None:
        return "Hello, World!"
    return f"Hello, {name}!"


def add_numbers(a: int, b: int) -> int:
    """Add two numbers together.
    
    Args:
        a: First number to add.
        b: Second number to add.
        
    Returns:
        The sum of a and b.
        
    Examples:
        >>> add_numbers(2, 3)
        5
        >>> add_numbers(-1, 1)
        0
    """
    return a + b
