[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "pystarter"
version = "0.1.0"
description = "A modern Python project starter template"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.11"
dependencies = []

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.0",
    "ruff>=0.1.0",
    "pyright>=1.1.0",
    "pre-commit>=3.4.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/pystarter"
Repository = "https://github.com/yourusername/pystarter"
Issues = "https://github.com/yourusername/pystarter/issues"

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.0",
    "ruff>=0.1.0",
    "pyright>=1.1.0",
    "pre-commit>=3.4.0",
]

# Ruff configuration
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG", # flake8-unused-arguments
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
    "PTH", # flake8-use-pathlib
    "ERA", # eradicate
    "PL",  # pylint
    "RUF", # ruff-specific rules
]
ignore = [
    "E501",   # line too long, handled by black
    "PLR0913", # too many arguments to function call
    "PLR0912", # too many branches
    "PLR0915", # too many statements
]
unfixable = [
    "ERA001", # commented-out code
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.ruff.isort]
known-first-party = ["pystarter"]

[tool.ruff.per-file-ignores]
"tests/**/*" = ["PLR2004", "S101", "TID252"]

# Pyright configuration
[tool.pyright]
include = ["src", "tests"]
exclude = ["**/__pycache__"]
venvPath = "."
venv = ".venv"
pythonVersion = "3.11"
pythonPlatform = "Linux"

# Strict mode settings
typeCheckingMode = "strict"
reportMissingImports = true
reportMissingTypeStubs = false
reportUnusedImport = true
reportUnusedClass = true
reportUnusedFunction = true
reportUnusedVariable = true
reportDuplicateImport = true
reportOptionalSubscript = true
reportOptionalMemberAccess = true
reportOptionalCall = true
reportOptionalIterable = true
reportOptionalContextManager = true
reportOptionalOperand = true
reportTypedDictNotRequiredAccess = false
reportPrivateImportUsage = true
reportConstantRedefinition = true
reportIncompatibleMethodOverride = true
reportIncompatibleVariableOverride = true
reportOverlappingOverloads = true
reportUntypedFunctionDecorator = true
reportUnknownParameterType = true
reportUnknownArgumentType = true
reportUnknownLambdaType = true
reportUnknownVariableType = true
reportUnknownMemberType = false
reportMissingParameterType = true
reportMissingTypeArgument = true
reportInvalidTypeVarUse = true
reportCallInDefaultInitializer = true
reportUnnecessaryIsInstance = true
reportUnnecessaryCast = true
reportUnnecessaryComparison = true
reportAssertAlwaysTrue = true
reportSelfClsParameterName = true
reportImplicitStringConcatenation = false
reportUndefinedVariable = true
reportUnboundVariable = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
branch = true
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"
