{
    "name": "Python uv Development",
    "build": {
        "dockerfile": "Dockerfile",
        "context": ".."
    },
    "workspaceFolder": "/workspace",
    "workspaceMount": "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached",
    
    // Configure tool-specific properties
    "customizations": {
        "vscode": {
            "settings": {
                "python.defaultInterpreterPath": "/workspace/.venv/bin/python",
                "python.terminal.activateEnvironment": true,
                "python.linting.enabled": true,
                "python.linting.pylintEnabled": false,
                "python.formatting.provider": "none",
                "python.analysis.typeCheckingMode": "strict",
                "python.analysis.autoImportCompletions": true,
                "python.analysis.autoSearchPaths": true,
                "python.analysis.diagnosticMode": "workspace",
                "python.analysis.stubPath": "typings",
                "python.testing.pytestEnabled": true,
                "python.testing.unittestEnabled": false,
                "python.testing.pytestArgs": [
                    "tests"
                ],
                "[python]": {
                    "editor.defaultFormatter": "charliermarsh.ruff",
                    "editor.formatOnSave": true,
                    "editor.codeActionsOnSave": {
                        "source.organizeImports": "explicit",
                        "source.fixAll": "explicit"
                    }
                },
                "ruff.organizeImports": true,
                "ruff.fixAll": true,
                "files.exclude": {
                    "**/__pycache__": true,
                    "**/*.pyc": true,
                    ".pytest_cache": true,
                    ".coverage": true,
                    "htmlcov": true,
                    ".ruff_cache": true
                }
            },
            "extensions": [
                "ms-python.python",
                "ms-python.vscode-pylance",
                "charliermarsh.ruff",
                "ms-python.pytest",
                "tamasfe.even-better-toml",
                "ms-vscode.vscode-json",
                "redhat.vscode-yaml",
                "ms-vscode.test-adapter-converter",
                "hbenl.vscode-test-explorer",
                "littlefoxteam.vscode-python-test-adapter",
                "ms-vscode.vscode-typescript-next"
            ]
        }
    },
    
    // Use 'forwardPorts' to make a list of ports inside the container available locally
    "forwardPorts": [],
    
    // Use 'postCreateCommand' to run commands after the container is created
    "postCreateCommand": "uv sync --dev && uv run pre-commit install",
    
    // Configure container user
    "remoteUser": "vscode",
    
    // Set container environment variables
    "containerEnv": {
        "PYTHONPATH": "/workspace/src"
    },
    
    // Mount the local .git folder to enable git operations
    "mounts": [
        "source=${localWorkspaceFolder}/.git,target=/workspace/.git,type=bind,consistency=cached"
    ],
    
    // Features to add to the dev container
    "features": {
        "ghcr.io/devcontainers/features/git:1": {},
        "ghcr.io/devcontainers/features/github-cli:1": {}
    }
}
