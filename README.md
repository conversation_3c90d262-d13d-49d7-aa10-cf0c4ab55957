# PyStarter

A modern Python project starter template using uv for dependency management and development tools.

## Features

- 🚀 **Modern Python**: Python 3.11+ with type hints and modern practices
- 📦 **uv**: Fast Python package installer and resolver
- 🔧 **Development Tools**: Pre-configured with ruff, pyright, pytest, and pre-commit
- 🐳 **Dev Container**: Ready-to-use development container with VS Code integration
- 📊 **Testing**: Comprehensive testing setup with pytest and coverage
- 🎯 **Type Safety**: Strict type checking with pyright
- 📝 **Code Quality**: Automated formatting and linting with ruff

## Quick Start

### Using Dev Container (Recommended)

1. Open this project in VS Code
2. When prompted, click "Reopen in Container"
3. The development environment will be automatically set up

### Local Development

1. Install [uv](https://docs.astral.sh/uv/):
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. Create and activate virtual environment:
   ```bash
   uv sync --dev
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. Install pre-commit hooks:
   ```bash
   uv run pre-commit install
   ```

## Development

### Running Tests

```bash
# Run all tests
uv run pytest

# Run tests with coverage
uv run pytest --cov

# Run tests in parallel
uv run pytest -n auto
```

### Code Quality

```bash
# Format code
uv run ruff format

# Lint code
uv run ruff check

# Fix linting issues
uv run ruff check --fix

# Type checking
uv run pyright
```

### Pre-commit Hooks

Pre-commit hooks are automatically installed and will run on every commit:

- Code formatting with ruff
- Linting with ruff
- Type checking with pyright
- Test execution

## Project Structure

```
pystarter/
├── .devcontainer/          # Dev container configuration
│   ├── devcontainer.json
│   └── Dockerfile
├── src/
│   └── pystarter/          # Main package
│       ├── __init__.py
│       ├── core.py
│       └── py.typed        # PEP 561 marker file
├── tests/                  # Test files
│   ├── __init__.py
│   └── test_core.py
├── pyproject.toml          # Project configuration
├── README.md
└── .gitignore
```

## Configuration

### pyproject.toml

The `pyproject.toml` file contains all project configuration:

- **Build system**: Uses hatchling for building
- **Dependencies**: Managed by uv
- **Development tools**: ruff, pyright, pytest configuration
- **Type checking**: Strict mode enabled
- **Testing**: pytest with coverage and parallel execution

### Development Tools

- **ruff**: Fast Python linter and formatter
- **pyright**: Static type checker in strict mode
- **pytest**: Testing framework with coverage
- **pre-commit**: Git hooks for code quality

## Adding Dependencies

```bash
# Add runtime dependency
uv add requests

# Add development dependency
uv add --dev black

# Add optional dependency group
uv add --optional-group docs sphinx
```

## License

MIT License - see LICENSE file for details.
